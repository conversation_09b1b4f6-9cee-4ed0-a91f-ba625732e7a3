import React, { useRef, useEffect, useState } from 'react';
import { Job, MapMarker } from '../types';
import { MapPin, Plus, Minus, Satellite } from 'lucide-react';
import Map from 'ol/Map';
import View from 'ol/View';
import TileLayer from 'ol/layer/Tile';
import OSM from 'ol/source/OSM';
import XYZ from 'ol/source/XYZ';
import VectorLayer from 'ol/layer/Vector';
import VectorSource from 'ol/source/Vector';
import Feature from 'ol/Feature';
import Point from 'ol/geom/Point';
import { fromLonLat } from 'ol/proj';
import { Style, Icon } from 'ol/style';
import Overlay from 'ol/Overlay';
import 'ol/ol.css';

interface MapContainerProps {
  jobs: Job[];
  selectedJob: Job | null;
  onJobSelect: (job: Job | null) => void;
  onMarkerClick: (jobs: Job[]) => void;
}

export const MapContainer: React.FC<MapContainerProps> = ({
  jobs,
  selectedJob,
  onJobSelect,
  onMarkerClick
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<Map | null>(null);
  const vectorSourceRef = useRef<VectorSource | null>(null);
  const popupRef = useRef<HTMLDivElement>(null);
  const popupOverlayRef = useRef<Overlay | null>(null);
  const osmLayerRef = useRef<TileLayer<OSM> | null>(null);
  const satelliteLayerRef = useRef<TileLayer<XYZ> | null>(null);

  const [isSatelliteView, setIsSatelliteView] = useState(false);

  // Group jobs by location
  const markers: MapMarker[] = jobs.reduce((acc, job) => {
    const existingMarker = acc.find(
      marker =>
        Math.abs(marker.position.lat - job.coordinates.lat) < 0.001 &&
        Math.abs(marker.position.lng - job.coordinates.lng) < 0.001
    );

    if (existingMarker) {
      existingMarker.jobCount++;
    } else {
      acc.push({
        id: job.id,
        position: job.coordinates,
        company: {
          id: job.id,
          name: job.company,
          logo: job.companyLogo,
          description: '',
          website: '',
          industry: job.industry,
          size: '',
          location: job.location,
          coordinates: job.coordinates,
          jobs: jobs.filter(j => j.location === job.location)
        },
        jobCount: jobs.filter(j => j.location === job.location).length
      });
    }
    return acc;
  }, [] as MapMarker[]);

  // Initialize map
  useEffect(() => {
    if (!mapRef.current) return;

    // Create vector source for markers
    const vectorSource = new VectorSource();
    vectorSourceRef.current = vectorSource;

    // Create vector layer for markers
    const vectorLayer = new VectorLayer({
      source: vectorSource,
    });

    // Create base layers
    const osmLayer = new TileLayer({
      source: new OSM(),
    });
    osmLayerRef.current = osmLayer;

    const satelliteLayer = new TileLayer({
      source: new XYZ({
        url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
        attributions: 'Tiles © Esri — Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community'
      }),
      visible: false,
    });
    satelliteLayerRef.current = satelliteLayer;

    // Create map
    const map = new Map({
      target: mapRef.current,
      layers: [
        osmLayer,
        satelliteLayer,
        vectorLayer,
      ],
      view: new View({
        center: fromLonLat([-98.5795, 39.8283]), // US center
        zoom: 4,
      }),
    });

    mapInstanceRef.current = map;

    // Create popup overlay
    if (popupRef.current) {
      const popupOverlay = new Overlay({
        element: popupRef.current,
        autoPan: true,
      });
      map.addOverlay(popupOverlay);
      popupOverlayRef.current = popupOverlay;
    }

    // Handle map clicks
    map.on('click', (event) => {
      const feature = map.forEachFeatureAtPixel(event.pixel, (feature) => feature);
      if (feature) {
        const markerData = feature.get('markerData') as MapMarker;
        if (markerData) {
          handleMarkerClick(markerData);
        }
      } else {
        // Hide popup when clicking on empty area
        hidePopup();
      }
    });

    // Handle pointer move for hover effects (desktop only)
    map.on('pointermove', (event) => {
      const feature = map.forEachFeatureAtPixel(event.pixel, (feature) => feature);
      map.getTargetElement().style.cursor = feature ? 'pointer' : '';

      // Only show hover tooltips on desktop
      const isMobile = window.innerWidth < 768;
      if (!isMobile && feature && popupOverlayRef.current) {
        const markerData = feature.get('markerData') as MapMarker;
        if (markerData) {
          showPopup(event.coordinate, markerData);
        }
      } else if (!isMobile) {
        hidePopup();
      }
    });

    return () => {
      map.setTarget(undefined);
    };
  }, []);

  const handleMarkerClick = (marker: MapMarker) => {
    const locationJobs = jobs.filter(job => job.location === marker.company.location);
    onMarkerClick(locationJobs);
    if (locationJobs.length === 1) {
      onJobSelect(locationJobs[0]);
    }
  };

  const showPopup = (coordinate: number[], marker: MapMarker) => {
    if (popupOverlayRef.current && popupRef.current) {
      // Update popup content
      const popupContent = popupRef.current.querySelector('.popup-content');
      if (popupContent) {
        popupContent.innerHTML = `
          <div class="text-sm font-semibold text-gray-900">${marker.company.name}</div>
          <div class="text-xs text-gray-600">${marker.company.location}</div>
          <div class="text-xs text-blue-600 mt-1">
            ${marker.jobCount} job${marker.jobCount > 1 ? 's' : ''} available
          </div>
        `;
      }
      // Show popup and set position
      popupRef.current.style.display = 'block';
      popupOverlayRef.current.setPosition(coordinate);
    }
  };

  const hidePopup = () => {
    if (popupOverlayRef.current && popupRef.current) {
      popupRef.current.style.display = 'none';
      popupOverlayRef.current.setPosition(undefined);
    }
  };

  // Create marker style
  const createMarkerStyle = (marker: MapMarker, isSelected: boolean) => {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    if (!context) return null;

    // Make markers slightly larger on mobile for better touch interaction
    const isMobile = window.innerWidth < 768;
    const width = isMobile ? 44 : 40;
    const height = isMobile ? 55 : 50;

    canvas.width = width;
    canvas.height = height;

    const centerX = width / 2;
    const centerY = height * 0.3;
    const radius = isMobile ? 14 : 12;

    // Draw marker pin
    context.fillStyle = isSelected ? '#f97316' : '#2563eb';
    context.beginPath();
    context.arc(centerX, centerY, radius, 0, 2 * Math.PI);
    context.fill();

    // Draw pin point
    context.beginPath();
    context.moveTo(centerX, centerY + radius + 2);
    context.lineTo(centerX - radius * 0.6, centerY);
    context.lineTo(centerX + radius * 0.6, centerY);
    context.closePath();
    context.fill();

    // Draw job count badge if multiple jobs
    if (marker.jobCount > 1) {
      const badgeRadius = isMobile ? 9 : 8;
      const badgeX = width - badgeRadius - 2;
      const badgeY = badgeRadius + 2;

      context.fillStyle = '#f97316';
      context.beginPath();
      context.arc(badgeX, badgeY, badgeRadius, 0, 2 * Math.PI);
      context.fill();

      context.fillStyle = 'white';
      context.font = `bold ${isMobile ? 11 : 10}px Arial`;
      context.textAlign = 'center';
      context.fillText(marker.jobCount.toString(), badgeX, badgeY + 4);
    }

    return new Style({
      image: new Icon({
        img: canvas,
        size: [width, height],
        anchor: [0.5, 1],
      }),
    });
  };

  // Update markers when jobs or selectedJob changes
  useEffect(() => {
    if (!vectorSourceRef.current) return;

    // Clear existing features
    vectorSourceRef.current.clear();

    // Add markers
    markers.forEach((marker) => {
      const feature = new Feature({
        geometry: new Point(fromLonLat([marker.position.lng, marker.position.lat])),
      });

      const isSelected = selectedJob?.location === marker.company.location;
      const style = createMarkerStyle(marker, isSelected);

      if (style) {
        feature.setStyle(style);
      }

      feature.set('markerData', marker);
      vectorSourceRef.current!.addFeature(feature);
    });
  }, [markers, selectedJob]);

  // Handle window resize to update marker sizes
  useEffect(() => {
    const handleResize = () => {
      // Trigger marker re-render on resize
      if (vectorSourceRef.current) {
        const features = vectorSourceRef.current.getFeatures();
        features.forEach((feature) => {
          const markerData = feature.get('markerData') as MapMarker;
          if (markerData) {
            const isSelected = selectedJob?.location === markerData.company.location;
            const style = createMarkerStyle(markerData, isSelected);
            if (style) {
              feature.setStyle(style);
            }
          }
        });
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [selectedJob]);

  // Zoom controls
  const handleZoomIn = () => {
    if (mapInstanceRef.current) {
      const view = mapInstanceRef.current.getView();
      const currentZoom = view.getZoom() || 4;
      view.setZoom(Math.min(currentZoom + 1, 18));
    }
  };

  const handleZoomOut = () => {
    if (mapInstanceRef.current) {
      const view = mapInstanceRef.current.getView();
      const currentZoom = view.getZoom() || 4;
      view.setZoom(Math.max(currentZoom - 1, 1));
    }
  };

  // Toggle between satellite and street view
  const toggleSatelliteView = () => {
    if (osmLayerRef.current && satelliteLayerRef.current) {
      const newIsSatellite = !isSatelliteView;
      setIsSatelliteView(newIsSatellite);

      if (newIsSatellite) {
        osmLayerRef.current.setVisible(false);
        satelliteLayerRef.current.setVisible(true);
      } else {
        osmLayerRef.current.setVisible(true);
        satelliteLayerRef.current.setVisible(false);
      }
    }
  };

  return (
    <div className="relative w-full h-full overflow-hidden">
      {/* OpenLayers Map */}
      <div
        ref={mapRef}
        className="w-full h-full"
      />

      {/* Popup for hover tooltips */}
      <div
        ref={popupRef}
        className="bg-white rounded-lg shadow-lg p-2 md:p-3 min-w-32 md:min-w-48 pointer-events-none text-xs md:text-sm"
        style={{ display: 'none' }}
      >
        <div className="popup-content"></div>
        <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-3 border-r-3 border-t-3 md:border-l-4 md:border-r-4 md:border-t-4 border-transparent border-t-white"></div>
      </div>

      {/* Map Controls */}
      <div className="absolute top-2 right-2 md:top-4 md:right-4 flex flex-col space-y-1 md:space-y-2 z-10">
        {/* Satellite Toggle */}
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <button
            onClick={toggleSatelliteView}
            className={`block p-2 md:p-2 hover:bg-gray-50 active:bg-gray-100 transition-colors duration-200 touch-manipulation ${
              isSatelliteView ? 'bg-blue-50 text-blue-600' : 'text-gray-600'
            }`}
            title={isSatelliteView ? 'Switch to Street View' : 'Switch to Satellite View'}
            aria-label={isSatelliteView ? 'Switch to Street View' : 'Switch to Satellite View'}
          >
            <Satellite size={18} className="md:w-5 md:h-5" />
          </button>
        </div>

        {/* Zoom Controls */}
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <button
            onClick={handleZoomIn}
            className="block p-2 md:p-2 hover:bg-gray-50 active:bg-gray-100 transition-colors duration-200 border-b border-gray-200 touch-manipulation"
            title="Zoom In"
            aria-label="Zoom In"
          >
            <Plus size={18} className="md:w-5 md:h-5 text-gray-600" />
          </button>
          <button
            onClick={handleZoomOut}
            className="block p-2 md:p-2 hover:bg-gray-50 active:bg-gray-100 transition-colors duration-200 touch-manipulation"
            title="Zoom Out"
            aria-label="Zoom Out"
          >
            <Minus size={18} className="md:w-5 md:h-5 text-gray-600" />
          </button>
        </div>
      </div>

      {/* Map Legend */}
      <div className="absolute bottom-2 left-2 md:bottom-4 md:left-4 bg-white rounded-lg shadow-lg p-2 md:p-3 z-10">
        <div className="text-xs font-semibold text-gray-900 mb-1 md:mb-2 hidden sm:block">Legend</div>
        <div className="flex items-center space-x-1 md:space-x-2 text-xs text-gray-600">
          <MapPin size={14} className="md:w-4 md:h-4 text-blue-600" />
          <span className="hidden sm:inline">Available Jobs</span>
          <span className="sm:hidden">Jobs</span>
        </div>
      </div>
    </div>
  );
};